#!/usr/bin/env python3
"""
单页面工作流测试应用
专门测试新的单页面工作流功能
"""

import streamlit as st
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.single_page_workflow import single_page_workflow
from src.ui.sidebar import display_sidebar

def main():
    """主函数"""
    st.set_page_config(
        page_title="单页面工作流测试",
        page_icon="🌊",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 显示侧边栏
    display_sidebar()
    
    # 主内容 - 单页面工作流
    single_page_workflow()

if __name__ == "__main__":
    main()
