"""
Jenkins集成模块
实现Jenkins API调用，包括job触发、参数传递和状态监控
"""

import jenkins
import time
import requests
from typing import Dict, Any, Optional, List
import os
from dotenv import load_dotenv
import logging

load_dotenv()

logger = logging.getLogger(__name__)

class JenkinsClient:
    """Jenkins客户端，负责与Jenkins服务器交互"""
    
    def __init__(self):
        self.server = jenkins.<PERSON>(
            url=os.getenv('JENKINS_URL'),
            username=os.getenv('JENKINS_USERNAME'),
            password=os.getenv('JENKINS_TOKEN')
        )
        self.verify_connection()
    
    def verify_connection(self):
        """验证Jenkins连接"""
        try:
            user = self.server.get_whoami()
            logger.info(f"Jenkins连接成功，当前用户: {user}")
        except Exception as e:
            logger.error(f"Jenkins连接失败: {e}")
            raise
    
    def trigger_job(self, job_name: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """触发Jenkins job"""
        try:
            if parameters:
                queue_item_number = self.server.build_job(job_name, parameters)
            else:
                queue_item_number = self.server.build_job(job_name)
            
            logger.info(f"Job {job_name} 已触发，队列号: {queue_item_number}")
            
            # 等待job开始执行并获取build号
            build_number = self._wait_for_build_start(job_name, queue_item_number)
            
            return {
                "success": True,
                "job_name": job_name,
                "build_number": build_number,
                "queue_item_number": queue_item_number,
                "message": f"Job {job_name} 已成功触发"
            }
            
        except Exception as e:
            logger.error(f"触发Job {job_name} 失败: {e}")
            return {
                "success": False,
                "job_name": job_name,
                "message": f"触发失败: {str(e)}"
            }
    
    def _wait_for_build_start(self, job_name: str, queue_item_number: int, timeout: int = 300) -> Optional[int]:
        """等待build开始执行并返回build号"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                queue_item = self.server.get_queue_item(queue_item_number)
                if 'executable' in queue_item:
                    build_number = queue_item['executable']['number']
                    logger.info(f"Job {job_name} 开始执行，Build号: {build_number}")
                    return build_number
                
                time.sleep(5)  # 等待5秒后重试
                
            except Exception as e:
                logger.warning(f"获取队列信息失败: {e}")
                time.sleep(5)
        
        logger.warning(f"等待Job {job_name} 开始执行超时")
        return None
    
    def get_build_status(self, job_name: str, build_number: int) -> Dict[str, Any]:
        """获取build状态"""
        try:
            build_info = self.server.get_build_info(job_name, build_number)

            return {
                "success": True,
                "job_name": job_name,
                "build_number": build_number,
                "status": "RUNNING" if build_info['building'] else build_info['result'],
                "duration": build_info.get('duration', 0),
                "timestamp": build_info.get('timestamp', 0),
                "url": build_info.get('url', ''),
                "building": build_info['building'],
                "build_info": build_info  # 添加完整的build信息
            }

        except Exception as e:
            logger.error(f"获取Build状态失败: {e}")
            return {
                "success": False,
                "message": f"获取状态失败: {str(e)}"
            }

    def get_pipeline_stages(self, job_name: str, build_number: int) -> Dict[str, Any]:
        """获取Pipeline的stage信息"""
        try:
            # 使用Jenkins API获取workflow信息
            jenkins_url = os.getenv('JENKINS_URL')
            jenkins_username = os.getenv('JENKINS_USERNAME')
            jenkins_token = os.getenv('JENKINS_TOKEN')

            # 构建API URL
            api_url = f"{jenkins_url}/job/{job_name}/{build_number}/wfapi/describe"

            # 发送请求
            response = requests.get(
                api_url,
                auth=(jenkins_username, jenkins_token),
                headers={'Accept': 'application/json'},
                timeout=30
            )

            if response.status_code == 200:
                workflow_data = response.json()
                stages = []
                current_stage = None

                # 解析stage信息
                if 'stages' in workflow_data:
                    for stage in workflow_data['stages']:
                        stage_info = {
                            "id": stage.get('id', ''),
                            "name": stage.get('name', ''),
                            "status": stage.get('status', ''),
                            "start_time": stage.get('startTimeMillis', 0),
                            "duration": stage.get('durationMillis', 0),
                            "pause_duration": stage.get('pauseDurationMillis', 0)
                        }
                        stages.append(stage_info)

                        # 确定当前正在执行的stage
                        if stage.get('status') == 'IN_PROGRESS':
                            current_stage = stage_info

                return {
                    "success": True,
                    "job_name": job_name,
                    "build_number": build_number,
                    "stages": stages,
                    "current_stage": current_stage,
                    "total_stages": len(stages)
                }
            else:
                # 如果workflow API不可用，尝试从控制台日志中解析stage信息
                return self._parse_stages_from_console(job_name, build_number)

        except Exception as e:
            logger.warning(f"获取Pipeline stages失败，尝试从控制台解析: {e}")
            # 降级到从控制台日志解析
            return self._parse_stages_from_console(job_name, build_number)

    def _parse_stages_from_console(self, job_name: str, build_number: int) -> Dict[str, Any]:
        """从控制台日志中解析stage信息"""
        try:
            console_result = self.get_console_output(job_name, build_number)

            if not console_result["success"]:
                return {
                    "success": False,
                    "message": "无法获取控制台输出"
                }

            console_output = console_result["console_output"]
            stages = []
            current_stage = None

            # 解析常见的stage标记
            stage_patterns = [
                "Stage '",
                "[Pipeline] stage",
                "Starting stage:",
                "Entering stage",
                "Running stage"
            ]

            lines = console_output.split('\n')
            for line in lines:
                line = line.strip()

                # 检查是否包含stage信息
                for pattern in stage_patterns:
                    if pattern in line:
                        # 提取stage名称
                        stage_name = self._extract_stage_name(line, pattern)
                        if stage_name:
                            stage_info = {
                                "id": f"stage_{len(stages)}",
                                "name": stage_name,
                                "status": "IN_PROGRESS",  # 从日志解析时假设正在进行
                                "start_time": 0,
                                "duration": 0,
                                "pause_duration": 0
                            }
                            stages.append(stage_info)
                            current_stage = stage_info
                        break

            # 如果没有找到具体的stage，创建一个默认的
            if not stages:
                stages = [{
                    "id": "default_stage",
                    "name": "构建执行中",
                    "status": "IN_PROGRESS",
                    "start_time": 0,
                    "duration": 0,
                    "pause_duration": 0
                }]
                current_stage = stages[0]

            return {
                "success": True,
                "job_name": job_name,
                "build_number": build_number,
                "stages": stages,
                "current_stage": current_stage,
                "total_stages": len(stages),
                "parsed_from_console": True
            }

        except Exception as e:
            logger.error(f"从控制台解析stage信息失败: {e}")
            return {
                "success": False,
                "message": f"解析stage信息失败: {str(e)}"
            }

    def _extract_stage_name(self, line: str, pattern: str) -> Optional[str]:
        """从日志行中提取stage名称"""
        try:
            if "Stage '" in line:
                # 提取 Stage 'name' 格式
                start = line.find("Stage '") + 7
                end = line.find("'", start)
                if end > start:
                    return line[start:end]

            elif "[Pipeline] stage" in line:
                # 提取 [Pipeline] stage (name) 格式
                start = line.find("(") + 1
                end = line.find(")", start)
                if end > start:
                    return line[start:end]

            elif any(p in line for p in ["Starting stage:", "Entering stage", "Running stage"]):
                # 提取 Starting stage: name 格式
                parts = line.split(":")
                if len(parts) > 1:
                    return parts[1].strip()

            return None

        except Exception:
            return None
    
    def get_console_output(self, job_name: str, build_number: int, start: int = 0) -> Dict[str, Any]:
        """获取控制台输出"""
        try:
            # Jenkins API的get_build_console_output方法只接受job_name和build_number两个参数
            console_output = self.server.get_build_console_output(job_name, build_number)

            # 如果需要从特定位置开始，我们手动截取
            if start > 0 and console_output:
                # 将字符串按字节截取（注意UTF-8编码）
                console_bytes = console_output.encode('utf-8')
                if start < len(console_bytes):
                    console_output = console_bytes[start:].decode('utf-8', errors='ignore')
                else:
                    console_output = ""

            return {
                "success": True,
                "job_name": job_name,
                "build_number": build_number,
                "console_output": console_output,
                "start_position": start
            }

        except Exception as e:
            logger.error(f"获取控制台输出失败: {e}")
            return {
                "success": False,
                "message": f"获取控制台输出失败: {str(e)}"
            }
    
    def monitor_build(self, job_name: str, build_number: int, callback=None) -> Dict[str, Any]:
        """监控build执行过程"""
        logger.info(f"开始监控Job {job_name} Build {build_number}")

        last_console_position = 0
        full_console_output = ""

        while True:
            # 获取build状态
            status_info = self.get_build_status(job_name, build_number)

            if not status_info["success"]:
                return status_info

            # 获取完整的控制台输出
            console_info = self.get_console_output(job_name, build_number, 0)

            if console_info["success"] and console_info["console_output"]:
                current_output = console_info["console_output"]

                # 检查是否有新的输出
                if len(current_output) > len(full_console_output):
                    new_output = current_output[len(full_console_output):]
                    full_console_output = current_output

                    if callback and new_output:
                        callback(job_name, build_number, new_output, status_info)

            # 检查是否完成
            if not status_info["building"]:
                logger.info(f"Job {job_name} Build {build_number} 执行完成，状态: {status_info['status']}")
                return status_info

            time.sleep(10)  # 每10秒检查一次

class JenkinsJobGenerator:
    """Jenkins Job生成器，根据发布计划生成Jenkins job列表"""
    
    # Jenkins job配置映射
    JOB_CONFIGS = {
        "aries": {
            "job_name": "Aries-Deploy",
            "params": {
                "infraBranch": "master",
                "AriesBranch": "{release_tag}"
            }
        },
        "canis": {
            "job_name": "Canis-K8S",
            "params": {
                "EnvironmentName": "open",
                "CanisCodeBranch": "{release_tag}",
                "InfraBranch": "{release_branch}"
            }
        },
        "em": {
            "job_name": "ProdCsmc",
            "params": {
                "EnvironmentType": "prod",
                "TenantName": "veeva",
                "EksCluster": "sfa-bj-prod-eks",
                "CodeBranch": "{release_tag}",
                "InfraCodeBranch": "{release_branch}"
            }
        },
        "openlog": {
            "job_name": "Prod-K8S-Tracing",
            "params": {
                "ClusterName": "sfa-bj-prod-eks",
                "Environment": "prod"
            }
        },
        "pisces": {
            "job_name": "IaC-terraform-SageMaker",
            "params": {
                "ACTION": "deploy",
                "InfraBranch": "{release_branch}",
                "Region": "cn-north-1",
                "Tenants": "shared"
            }
        },
        "chinacrm": {
            "job_name": "{tenant_name}",
            "params": {
                "TenantName": "{tenant_name}",
                "ProdCodeBranch": "{release_tag}",
                "NeedDeployPS": "{need_deploy_ps}",
                "InfraBranch": "{release_branch}",
                "PSCodeBranch": "{release_branch}",
                "DryRun": "false"
            }
        },
        "lumos": {
            "job_name": "lumos-k8s-deployment",
            "params": {
                "Account": "prod",
                "EksCluster": "sfa-bj-prod-eks",
                "Region": "cn-north-1",
                "Environment": "prod",
                "Tenant": "{tenant_name}",
                "InfraBranch": "{release_branch}",
                "Branch": "{release_tag}",
                "ImageTag": "{image_tag}",
                "DryRun": "false",
                "Node": "ec2-slave"
            }
        },
        "taurus": {
            "job_name": "Prod-Taurus",
            "params": {
                "TenantName": "{tenant_name}",
                "TaurusCodeBranch": "{release_tag}",
                "InfraBranch": "{release_branch}",
                "DryRun": "false"
            }
        },
        "rigel": {
            "job_name": "Prod-Rigel",
            "params": {
                "TenantName": "{tenant_name}",
                "RigelCodeBranch": "{release_tag}",
                "InfraBranch": "{release_branch}",
                "DryRun": "false"
            }
        },
        "hydra": {
            "job_name": "Hydra-Single-Tenant-Deploy-Increment",
            "params": {
                "Tenant": "{tenant_name_lower}",
                "ProductDeploy": "true",
                "ProductBranch": "{release_tag}",
                "PSDeploy": "false",
                "PSBranch": "{release_branch}",
                "SlugFile": "",
                "FirePipelines": "false",
                "infraBranch": "{release_branch}"
            }
        },
        "mintaka": {
            "job_name": "Prod-Mintaka",
            "params": {
                "TenantName": "{tenant_name}",
                "ProdCodeBranch": "{release_tag}",
                "infraBranch": "{release_branch}",
                "Migrate": "true"
            }
        }
    }
    
    @staticmethod
    def generate_release_branch(version: str) -> str:
        """生成release分支名"""
        # 25R1.2 -> release/251.2
        version_clean = version.replace('R', '')
        return f"release/{version_clean}"

    @staticmethod
    def generate_release_tag(version: str) -> str:
        """生成release tag"""
        # 25R1.2 -> LR/251.2.0, 25R2.0 -> GR/252.0.0
        version_clean = version.replace('R', '')
        version_parts = version_clean.split('.')

        if len(version_parts) == 2:
            major = version_parts[0]
            minor = version_parts[1]
            if minor == '0':
                return f"GR/{major}.{minor}.0"
            else:
                return f"LR/{version_clean}.0"
        return f"LR/{version_clean}.0"

    def generate_jenkins_jobs(self, deployment_plan: List[Dict], version: str) -> List[Dict]:
        """根据发布计划生成Jenkins job列表"""
        jenkins_jobs = []
        release_branch = self.generate_release_branch(version)
        release_tag = self.generate_release_tag(version)

        for item in deployment_plan:
            service_name = item.get("Service名", "").lower()
            tenant_name = item.get("租户名", "")
            need_deploy_ps = "true" if item.get("是否部署PS代码") == "是" else "false"

            if service_name in self.JOB_CONFIGS:
                config = self.JOB_CONFIGS[service_name].copy()

                # 处理job名称
                job_name = config["job_name"]
                if "{tenant_name}" in job_name:
                    job_name = job_name.replace("{tenant_name}", tenant_name)

                # 处理参数
                params = {}
                for key, value in config["params"].items():
                    if isinstance(value, str):
                        value = value.replace("{release_branch}", release_branch)
                        value = value.replace("{release_tag}", release_tag)
                        value = value.replace("{tenant_name}", tenant_name)
                        value = value.replace("{tenant_name_lower}", tenant_name.lower())
                        value = value.replace("{need_deploy_ps}", need_deploy_ps)
                        value = value.replace("{image_tag}", release_tag)  # 默认使用release_tag作为image_tag
                    params[key] = value

                deployment_date = item.get("计划部署日期", "")
                if hasattr(deployment_date, 'strftime'):
                    deployment_date = deployment_date.strftime('%Y-%m-%d')

                jenkins_jobs.append({
                    "deployment_date": deployment_date,
                    "time_window": item.get("时间窗口", ""),
                    "customer_name": item.get("客户名", ""),
                    "tenant_name": tenant_name,
                    "service_name": item.get("Service名", ""),
                    "deployment_order": item.get("部署顺序", 999),
                    "job_name": job_name,
                    "parameters": params
                })

        # 按部署顺序排序Jenkins jobs
        # 排序规则：
        # 1. 按部署日期排序
        # 2. 按时间窗口排序
        # 3. 按客户名排序（多租户服务优先）
        # 4. 按部署顺序排序
        jenkins_jobs.sort(key=lambda x: (
            x.get("deployment_date", ""),
            x.get("time_window", ""),
            0 if x.get("customer_name") == "多租户服务" else 1,  # 多租户服务优先
            x.get("deployment_order", 999)
        ))

        logger.info(f"生成了 {len(jenkins_jobs)} 个Jenkins jobs，已按部署顺序排序")
        return jenkins_jobs
