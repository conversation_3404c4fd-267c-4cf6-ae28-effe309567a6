"""
LangGraph工作流设计
设计agent工作流：获取部署计划 -> 用户review -> 确认执行 -> Jenkins job触发 -> 监控执行
"""

from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel, Field
import logging
from enum import Enum

from .database import ReleaseQueryService
from .jenkins_client import JenkinsClient, JenkinsJobGenerator

logger = logging.getLogger(__name__)

class WorkflowState(BaseModel):
    """工作流状态"""
    # 输入参数
    version: str = ""
    environment: str = ""

    # 发布计划相关
    deployment_plan: Optional[List[Dict]] = None
    jenkins_jobs: Optional[List[Dict]] = None

    # 用户交互
    user_approved_plan: bool = False
    user_approved_execution: bool = False
    selected_jobs: Optional[List[Dict]] = None

    # 执行状态
    current_job_index: int = 0
    job_results: List[Dict] = Field(default_factory=list)
    execution_completed: bool = False

    # 顺序执行相关
    current_executing_job: Optional[Dict] = None
    current_build_number: Optional[int] = None
    waiting_for_completion: bool = False

    # 消息和错误
    messages: List[str] = Field(default_factory=list)
    errors: List[str] = Field(default_factory=list)

    # 当前步骤
    current_step: str = "start"

class ReleaseAgentWorkflow:
    """发布代理工作流"""
    
    def __init__(self):
        self.query_service = ReleaseQueryService()
        self.jenkins_client = JenkinsClient()
        self.job_generator = JenkinsJobGenerator()
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """构建工作流图"""
        workflow = StateGraph(WorkflowState)
        
        # 添加节点
        workflow.add_node("get_deployment_plan", self._get_deployment_plan)
        workflow.add_node("generate_jenkins_jobs", self._generate_jenkins_jobs)
        workflow.add_node("wait_for_plan_approval", self._wait_for_plan_approval)
        workflow.add_node("wait_for_execution_approval", self._wait_for_execution_approval)
        workflow.add_node("execute_jenkins_jobs", self._execute_jenkins_jobs)
        workflow.add_node("monitor_execution", self._monitor_execution)
        workflow.add_node("complete", self._complete)
        
        # 设置入口点
        workflow.set_entry_point("get_deployment_plan")
        
        # 添加边
        workflow.add_edge("get_deployment_plan", "generate_jenkins_jobs")
        workflow.add_edge("generate_jenkins_jobs", "wait_for_plan_approval")
        
        # 条件边：计划审批
        workflow.add_conditional_edges(
            "wait_for_plan_approval",
            self._should_proceed_to_execution,
            {
                "approved": "wait_for_execution_approval",
                "rejected": END,
                "waiting": END  # 避免无限循环，直接结束
            }
        )

        # 条件边：执行审批
        workflow.add_conditional_edges(
            "wait_for_execution_approval",
            self._should_start_execution,
            {
                "approved": "execute_jenkins_jobs",
                "rejected": END,
                "waiting": END  # 避免无限循环，直接结束
            }
        )
        
        workflow.add_edge("execute_jenkins_jobs", "monitor_execution")
        
        # 条件边：监控执行
        workflow.add_conditional_edges(
            "monitor_execution",
            self._should_continue_monitoring,
            {
                "continue": "monitor_execution",
                "complete": "complete"
            }
        )
        
        workflow.add_edge("complete", END)
        
        # 编译工作流，设置递归限制
        return workflow.compile(
            checkpointer=None,
            interrupt_before=None,
            interrupt_after=None,
            debug=False
        )
    
    def _get_deployment_plan(self, state: WorkflowState) -> WorkflowState:
        """获取部署计划"""
        logger.info(f"获取部署计划: {state.version} {state.environment}")
        
        try:
            result = self.query_service.get_deployment_plan(state.version, state.environment)
            
            if result["success"]:
                state.deployment_plan = result["data"]
                state.messages.append(f"成功获取 {state.version} {state.environment}环境 的发布计划")
                state.current_step = "plan_retrieved"
            else:
                state.errors.append(result["message"])
                state.current_step = "error"
                
        except Exception as e:
            error_msg = f"获取部署计划失败: {str(e)}"
            state.errors.append(error_msg)
            state.current_step = "error"
            logger.error(error_msg)
        
        return state
    
    def _generate_jenkins_jobs(self, state: WorkflowState) -> WorkflowState:
        """生成Jenkins jobs"""
        logger.info("生成Jenkins jobs")
        
        try:
            if state.deployment_plan:
                state.jenkins_jobs = self.job_generator.generate_jenkins_jobs(
                    state.deployment_plan, state.version
                )
                state.messages.append(f"成功生成 {len(state.jenkins_jobs)} 个Jenkins jobs")
                state.current_step = "jobs_generated"
            else:
                state.errors.append("没有可用的部署计划")
                state.current_step = "error"
                
        except Exception as e:
            error_msg = f"生成Jenkins jobs失败: {str(e)}"
            state.errors.append(error_msg)
            state.current_step = "error"
            logger.error(error_msg)
        
        return state
    
    def _wait_for_plan_approval(self, state: WorkflowState) -> WorkflowState:
        """等待计划审批"""
        state.current_step = "waiting_plan_approval"
        state.messages.append("等待用户审批发布计划...")
        return state
    
    def _wait_for_execution_approval(self, state: WorkflowState) -> WorkflowState:
        """等待执行审批"""
        state.current_step = "waiting_execution_approval"
        state.messages.append("等待用户确认执行Jenkins jobs...")
        return state
    
    def _execute_jenkins_jobs(self, state: WorkflowState) -> WorkflowState:
        """执行Jenkins jobs"""
        logger.info("开始执行Jenkins jobs")
        
        try:
            jobs_to_execute = state.selected_jobs or []
            if not jobs_to_execute:
                state.errors.append("没有选择要执行的Job")
                state.current_step = "completed"
                return state
            
            state.current_step = "executing"
            state.messages.append(f"开始执行 {len(jobs_to_execute)} 个Jenkins jobs")
            
            # 这里只是标记开始执行，实际执行在monitor_execution中进行
            state.job_results = []
            state.current_job_index = 0
            
        except Exception as e:
            error_msg = f"执行Jenkins jobs失败: {str(e)}"
            state.errors.append(error_msg)
            state.current_step = "error"
            logger.error(error_msg)
        
        return state
    
    def _monitor_execution(self, state: WorkflowState) -> WorkflowState:
        """监控执行 - 按部署顺序执行Jenkins jobs"""
        logger.info(f"监控执行进度: {state.current_job_index}, 等待完成: {state.waiting_for_completion}")

        try:
            jobs_to_execute = state.selected_jobs or state.jenkins_jobs or []

            if state.current_job_index >= len(jobs_to_execute):
                # 所有job都已完成
                state.execution_completed = True
                state.current_step = "completed"
                state.messages.append("所有Jenkins jobs执行完成")
                logger.info("所有Jenkins jobs执行完成")
                return state

            # 如果当前没有正在执行的job，启动下一个
            if not state.waiting_for_completion:
                current_job = jobs_to_execute[state.current_job_index]

                logger.info(f"开始执行Job: {current_job['job_name']}")
                state.messages.append(f"开始执行Job: {current_job['job_name']}")

                # 触发Jenkins job
                result = self.jenkins_client.trigger_job(
                    current_job["job_name"],
                    current_job["parameters"]
                )

                if result["success"]:
                    # 记录当前执行的job信息
                    state.current_executing_job = current_job
                    state.current_build_number = result.get("build_number")
                    state.waiting_for_completion = True
                    state.current_step = "executing"

                    state.messages.append(f"Job {current_job['job_name']} 已触发，Build号: {state.current_build_number}")
                    logger.info(f"Job {current_job['job_name']} 已触发，Build号: {state.current_build_number}")
                else:
                    # 触发失败，记录错误并继续下一个
                    error_msg = f"触发Job {current_job['job_name']} 失败: {result.get('message', '')}"
                    state.errors.append(error_msg)
                    state.job_results.append({
                        "job_name": current_job['job_name'],
                        "status": "FAILED",
                        "error": result.get('message', ''),
                        "build_number": None
                    })

                    # 移动到下一个job
                    state.current_job_index += 1
                    state.current_executing_job = None
                    state.current_build_number = None
                    logger.error(error_msg)

            # 如果有正在执行的job，检查其状态
            elif state.waiting_for_completion and state.current_executing_job:
                job_name = state.current_executing_job["job_name"]
                build_number = state.current_build_number

                if build_number:
                    # 检查job执行状态
                    status_result = self.jenkins_client.get_build_status(job_name, build_number)

                    if status_result["success"]:
                        building = status_result.get("building", True)
                        status = status_result.get("status", "RUNNING")

                        if not building:  # job已完成
                            # 记录执行结果
                            job_result = {
                                "job_name": job_name,
                                "build_number": build_number,
                                "status": status,
                                "duration": status_result.get("duration", 0),
                                "url": status_result.get("url", "")
                            }
                            state.job_results.append(job_result)

                            if status == "SUCCESS":
                                state.messages.append(f"✅ Job {job_name} 执行成功")
                                logger.info(f"Job {job_name} 执行成功")
                            else:
                                state.messages.append(f"❌ Job {job_name} 执行失败，状态: {status}")
                                logger.warning(f"Job {job_name} 执行失败，状态: {status}")

                            # 重置状态，准备执行下一个job
                            state.current_job_index += 1
                            state.current_executing_job = None
                            state.current_build_number = None
                            state.waiting_for_completion = False
                            state.current_step = "monitoring"
                        else:
                            # job仍在执行中
                            state.messages.append(f"🔄 Job {job_name} 正在执行中...")
                            state.current_step = "executing"
                    else:
                        # 获取状态失败
                        error_msg = f"获取Job {job_name} 状态失败: {status_result.get('message', '')}"
                        state.errors.append(error_msg)
                        logger.error(error_msg)

                        # 假设job失败，继续下一个
                        state.job_results.append({
                            "job_name": job_name,
                            "build_number": build_number,
                            "status": "UNKNOWN",
                            "error": status_result.get('message', '')
                        })

                        state.current_job_index += 1
                        state.current_executing_job = None
                        state.current_build_number = None
                        state.waiting_for_completion = False
                else:
                    # 没有build号，跳过这个job
                    error_msg = f"Job {job_name} 没有获取到Build号"
                    state.errors.append(error_msg)
                    logger.error(error_msg)

                    state.current_job_index += 1
                    state.current_executing_job = None
                    state.current_build_number = None
                    state.waiting_for_completion = False

        except Exception as e:
            error_msg = f"监控执行失败: {str(e)}"
            state.errors.append(error_msg)
            state.current_step = "error"
            logger.error(error_msg)

        return state
    
    def _complete(self, state: WorkflowState) -> WorkflowState:
        """完成工作流"""
        state.current_step = "complete"
        state.messages.append("工作流执行完成")
        logger.info("工作流执行完成")
        return state
    
    def _should_proceed_to_execution(self, state: WorkflowState) -> str:
        """判断是否应该进入执行阶段"""
        if state.user_approved_plan:
            return "approved"
        elif state.user_approved_plan is False:
            return "rejected"
        else:
            # 在演示模式下，自动批准计划
            logger.info("演示模式：自动批准发布计划")
            state.user_approved_plan = True
            return "approved"

    def _should_start_execution(self, state: WorkflowState) -> str:
        """判断是否应该开始执行"""
        if state.user_approved_execution:
            return "approved"
        elif state.user_approved_execution is False:
            return "rejected"
        else:
            # 在演示模式下，自动批准执行
            logger.info("演示模式：自动批准执行")
            state.user_approved_execution = True
            return "approved"
    
    def _should_continue_monitoring(self, state: WorkflowState) -> str:
        """判断是否应该继续监控"""
        if state.execution_completed:
            return "complete"
        else:
            return "continue"
    
    def run(self, version: str, environment: str) -> WorkflowState:
        """运行工作流"""
        initial_state = WorkflowState(
            version=version,
            environment=environment,
            current_step="start"
        )

        try:
            # 设置配置参数，增加递归限制
            config = {
                "recursion_limit": 50,  # 增加递归限制
                "max_execution_time": 300  # 最大执行时间5分钟
            }

            result = self.graph.invoke(initial_state, config=config)

            # 如果result是字典，需要转换为WorkflowState对象
            if isinstance(result, dict):
                # 创建新的WorkflowState对象并复制数据
                final_state = WorkflowState(
                    version=result.get('version', initial_state.version),
                    environment=result.get('environment', initial_state.environment),
                    current_step=result.get('current_step', 'unknown')
                )

                # 复制其他属性
                final_state.deployment_plan = result.get('deployment_plan', [])
                final_state.jenkins_jobs = result.get('jenkins_jobs', [])
                final_state.user_approved_plan = result.get('user_approved_plan', None)
                final_state.user_approved_execution = result.get('user_approved_execution', None)
                final_state.current_job_index = result.get('current_job_index', 0)
                final_state.job_results = result.get('job_results', [])
                final_state.execution_completed = result.get('execution_completed', False)
                final_state.messages = result.get('messages', [])
                final_state.errors = result.get('errors', [])

                return final_state
            else:
                return result

        except Exception as e:
            logger.error(f"工作流执行失败: {e}")
            initial_state.errors.append(f"工作流执行失败: {str(e)}")
            initial_state.current_step = "error"
            return initial_state
    
    def approve_plan(self, state: WorkflowState) -> WorkflowState:
        """用户审批发布计划"""
        state.user_approved_plan = True
        state.messages.append("用户已审批发布计划")

        # 继续执行工作流到下一个状态
        try:
            # 设置配置参数
            config = {
                "recursion_limit": 50,
                "max_execution_time": 300
            }

            # 继续执行工作流
            result = self.graph.invoke(state, config=config)

            # 如果result是字典，转换为WorkflowState对象
            if isinstance(result, dict):
                final_state = WorkflowState(
                    version=result.get('version', state.version),
                    environment=result.get('environment', state.environment),
                    current_step=result.get('current_step', 'unknown')
                )

                # 复制其他属性
                final_state.deployment_plan = result.get('deployment_plan', state.deployment_plan)
                final_state.jenkins_jobs = result.get('jenkins_jobs', state.jenkins_jobs)
                final_state.user_approved_plan = result.get('user_approved_plan', True)
                final_state.user_approved_execution = result.get('user_approved_execution', state.user_approved_execution)
                final_state.current_job_index = result.get('current_job_index', state.current_job_index)
                final_state.job_results = result.get('job_results', state.job_results)
                final_state.execution_completed = result.get('execution_completed', state.execution_completed)
                final_state.messages = result.get('messages', state.messages)
                final_state.errors = result.get('errors', state.errors)

                return final_state
            else:
                return result

        except Exception as e:
            logger.error(f"继续执行工作流失败: {e}")
            state.errors.append(f"继续执行工作流失败: {str(e)}")
            return state
    
    def approve_execution(self, state: WorkflowState, selected_jobs: Optional[List[Dict]] = None) -> WorkflowState:
        """用户审批执行"""
        state.user_approved_execution = True
        if selected_jobs:
            state.selected_jobs = selected_jobs
        state.messages.append("用户已确认执行")

        # 继续执行工作流到下一个状态
        try:
            # 设置配置参数
            config = {
                "recursion_limit": 50,
                "max_execution_time": 300
            }

            # 继续执行工作流
            result = self.graph.invoke(state, config=config)

            # 如果result是字典，转换为WorkflowState对象
            if isinstance(result, dict):
                final_state = WorkflowState(
                    version=result.get('version', state.version),
                    environment=result.get('environment', state.environment),
                    current_step=result.get('current_step', 'unknown')
                )

                # 复制其他属性
                final_state.deployment_plan = result.get('deployment_plan', state.deployment_plan)
                final_state.jenkins_jobs = result.get('jenkins_jobs', state.jenkins_jobs)
                final_state.user_approved_plan = result.get('user_approved_plan', state.user_approved_plan)
                final_state.user_approved_execution = result.get('user_approved_execution', True)
                final_state.current_job_index = result.get('current_job_index', state.current_job_index)
                final_state.job_results = result.get('job_results', state.job_results)
                final_state.execution_completed = result.get('execution_completed', state.execution_completed)
                final_state.messages = result.get('messages', state.messages)
                final_state.errors = result.get('errors', state.errors)

                return final_state
            else:
                return result

        except Exception as e:
            logger.error(f"继续执行工作流失败: {e}")
            state.errors.append(f"继续执行工作流失败: {str(e)}")
            return state
    
    def reject_plan(self, state: WorkflowState, reason: str = "") -> WorkflowState:
        """用户拒绝发布计划"""
        state.user_approved_plan = False
        state.messages.append(f"用户拒绝发布计划: {reason}")
        state.current_step = "rejected"
        return state
    
    def reject_execution(self, state: WorkflowState, reason: str = "") -> WorkflowState:
        """用户拒绝执行"""
        state.user_approved_execution = False
        state.messages.append(f"用户拒绝执行: {reason}")
        state.current_step = "rejected"
        return state
