import streamlit as st
from datetime import datetime
from src.logic.llm_utils import LLMProvider
from src.ui.components import (
    parse_deployment_plan_table,
    parse_task_datetime,
    analyze_job_time_windows,
    display_time_analysis_summary_enhanced,
    add_test_time_data
)

# Placeholder for plan_query_tab
def plan_query_tab():
    """发布计划查询标签页"""
    st.header("📝 发布计划查询")
    st.markdown("--- ")

    with st.container(border=True):
        col1, col2, col3 = st.columns([2, 1, 1])
        with col1:
            version = st.text_input(
                "**版本号**",
                placeholder="例如: 25R1.2",
                help="输入发布版本号，格式如 25R1.2"
            )
        with col2:
            environment = st.selectbox(
                "**环境**",
                ["Prod", "Staging", "Test", "Dev"],
                help="选择目标环境"
            )
        with col3:
            st.write("&nbsp;") # 占位符，让按钮和输入框在同一水平线上
            if st.button("🔍 查询", type="primary", use_container_width=True):
                if version and environment:
                    with st.spinner("正在查询发布计划..."):
                        query_deployment_plan(version, environment)
                else:
                    st.error("请输入版本号和选择环境")

    # 显示查询结果
    if 'deployment_plan' in st.session_state and st.session_state.deployment_plan:
        with st.expander("**查询结果**", expanded=True):
            display_deployment_plan()

def query_deployment_plan(version: str, environment: str):
    """查询发布计划"""
    try:
        # This should be handled by a proper agent or logic module
        # For now, we'll just simulate the result
        st.session_state.agent = st.session_state.get('agent', None)
        if not st.session_state.agent:
            from src.release_agent import ReleaseAgent
            st.session_state.agent = ReleaseAgent()

        user_input = f"列出 {version} {environment} 的发布计划"
        result = st.session_state.agent.process_user_request(user_input)
        
        if result["success"]:
            st.session_state.deployment_plan = result["data"]
            # 保存原始的jenkins_jobs
            if result["data"].get("raw_jenkins_jobs"):
                st.session_state.raw_jenkins_jobs = result["data"]["raw_jenkins_jobs"]
            st.success(result["message"])
        else:
            st.error(result["message"]) 
            
    except Exception as e:
        st.error(f"查询失败: {str(e)}")

def display_deployment_plan():
    """显示发布计划"""
    data = st.session_state.deployment_plan
    
    st.subheader(f"📋 {data['version']} {data['environment']}环境 发布计划")
    
    # 显示发布计划表格
    if "deployment_plan" in data:
        st.markdown(data["deployment_plan"])
    
    # 显示Jenkins jobs
    if "jenkins_jobs" in data:
        st.subheader("🔧 Jenkins Jobs")
        st.markdown(data["jenkins_jobs"])

def plan_approval_tab():
    """计划审批标签页"""
    st.header("✅ 计划审批中心")
    st.markdown("---")

    if 'deployment_plan' not in st.session_state or not st.session_state.deployment_plan:
        st.info("💡 请先在「发布计划」标签页查询一个发布计划，然后在这里进行审批。")
        return

    data = st.session_state.deployment_plan
    
    with st.container(border=True):
        display_filtered_deployment_plan(data)

def display_filtered_deployment_plan(data):
    """以更美观的方式显示按时间筛选的发布计划"""
    import datetime
    from datetime import datetime as dt

    deployment_plan_text = data.get("deployment_plan", "")
    if not deployment_plan_text:
        st.warning("没有发布计划数据")
        return

    plan_items = parse_deployment_plan_table(deployment_plan_text)
    if not plan_items:
        st.warning("无法解析发布计划数据")
        return

    now = dt.now()
    
    # --- Filter Controls and Stats ---
    st.subheader("🕒 时间筛选与统计")
    col1, col2, col3 = st.columns([2, 2, 3])
    with col1:
        filter_date = st.date_input("筛选基准日期", value=now.date(), help="选择用于筛选的基准日期")
    with col2:
        filter_time = st.time_input("筛选基准时间", value=now.time(), help="选择用于筛选的基准时间")

    # --- Filtering Logic ---
    filter_datetime = dt.combine(filter_date, filter_time)
    due_tasks = []
    future_tasks = []
    no_time_tasks = []

    for item in plan_items:
        task_datetime = parse_task_datetime(item)
        if task_datetime is None:
            no_time_tasks.append(item)
        elif task_datetime <= filter_datetime:
            due_tasks.append(item)
        else:
            future_tasks.append(item)

    with col3:
        st.write("&nbsp;") # for alignment
        st.write("&nbsp;")
        stat_cols = st.columns(3)
        stat_cols[0].metric("🔴 到期", len(due_tasks))
        stat_cols[1].metric("🟡 未来", len(future_tasks))
        stat_cols[2].metric("⚪️ 未定", len(no_time_tasks))

    st.markdown("---")

    # --- Task Lists ---
    if not due_tasks and not future_tasks and not no_time_tasks:
        st.info("当前筛选条件下没有任务。")
        return

    if due_tasks:
        with st.expander(f"🔴 **到期任务 ({len(due_tasks)})**", expanded=True):
            st.warning(f"⚠️ 有 {len(due_tasks)} 个任务已到部署时间，需要立即处理！")
            for task in due_tasks:
                task_desc = f"{task.get('客户名', 'N/A')} - {task.get('Service名', 'N/A')}"
                task_time = f"{task.get('计划部署日期', '')} {task.get('时间窗口', '')}"
                st.markdown(f"- **{task_desc}**: `{task_time.strip()}`")
    
    if future_tasks:
        with st.expander(f"🟡 **未来任务 ({len(future_tasks)})**", expanded=False):
            st.info(f"ℹ️ 有 {len(future_tasks)} 个任务计划在未来执行。")
            for task in future_tasks:
                task_desc = f"{task.get('客户名', 'N/A')} - {task.get('Service名', 'N/A')}"
                task_time = f"{task.get('计划部署日期', '')} {task.get('时间窗口', '')}"
                st.markdown(f"- **{task_desc}**: `{task_time.strip()}`")

    if no_time_tasks:
        with st.expander(f"⚪️ **无时间信息任务 ({len(no_time_tasks)})**", expanded=False):
            st.info(f"ℹ️ 有 {len(no_time_tasks)} 个任务没有明确的时间信息。")
            for task in no_time_tasks:
                task_desc = f"{task.get('客户名', 'N/A')} - {task.get('Service名', 'N/A')}"
                st.markdown(f"- **{task_desc}**")


def execution_management_tab():
    """执行管理标签页"""
    st.header("🚀 执行管理")
    st.markdown("---")

    if st.session_state.get('confirming_execution', False):
        display_execution_confirmation()
        return

    if 'deployment_plan' not in st.session_state or not st.session_state.deployment_plan:
        st.info("💡 请先在「发布计划」标签页查询一个发布计划，然后在这里执行构建任务。")
        return

    workflow_state = st.session_state.deployment_plan.get("workflow_state", "")
    if workflow_state not in ["plan_approved", "executing", "execution_completed"]:
        st.info("请先完成发布计划的审批步骤")
        return

    with st.container(border=True):
        if 'execution_status' not in st.session_state or not st.session_state.execution_status:
            display_jenkins_jobs_selection()
        else:
            if st.button("🔄 刷新状态"):
                refresh_execution_status()
            display_execution_status()

def display_jenkins_jobs_selection():
    """以更美观的方式显示Jenkins Jobs选择界面"""
    st.subheader("🔧 选择要执行的 Jenkins Jobs")

    if 'raw_jenkins_jobs' not in st.session_state or not st.session_state.raw_jenkins_jobs:
        st.warning("没有可用的 Jenkins Jobs。请先查询发布计划。")
        return

    all_jobs = st.session_state.raw_jenkins_jobs
    if st.checkbox("为演示添加测试时间数据"):
        all_jobs = add_test_time_data(all_jobs)

    current_time = datetime.now()
    time_analysis = analyze_job_time_windows(all_jobs, current_time)
    display_time_analysis_summary_enhanced(time_analysis, current_time)

    st.markdown("---")

    selected_indices = []
    job_categories = {
        "🟢 可部署": (time_analysis["deployable_jobs"], True),
        "🟡 未来执行": (time_analysis["future_jobs"], False),
        "🔴 已过期": (time_analysis["expired_jobs"], False),
        "⚪ 无时间限制": (time_analysis["no_time_jobs"], False)
    }

    for category, (jobs, default_selected) in job_categories.items():
        if jobs:
            with st.expander(f"**{category} ({len(jobs)})**", expanded=default_selected):
                for job_info in jobs:
                    job = job_info["job"]
                    job_index = job_info["index"]
                    
                    cols = st.columns([1, 5, 3])
                    with cols[0]:
                        if st.checkbox("", value=default_selected, key=f"job_{job_index}", label_visibility="collapsed"):
                            selected_indices.append(job_index)
                    
                    with cols[1]:
                        st.markdown(f"**{job.get('job_name', 'N/A')}**")
                        st.caption(f"{job.get('customer_name', 'N/A')} - {job.get('service_name', 'N/A')}")
                    
                    with cols[2]:
                        st.markdown(f"`{job.get('deployment_date', '无')} {job.get('time_window', '无')}`")
                        if job_info['time_status'].get('time_remaining'):
                            st.success(f"剩余: {job_info['time_status']['time_remaining']}")
                        if job_info['time_status'].get('time_until_start'):
                            st.info(f"开始于: {job_info['time_status']['time_until_start']}")
                        if job_info['time_status'].get('time_since_end'):
                            st.error(f"已结束: {job_info['time_status']['time_since_end']}")

    st.markdown("---")

    # 获取各类别的数量
    deployable_count = len(time_analysis["deployable_jobs"])
    future_count = len(time_analysis["future_jobs"])
    expired_count = len(time_analysis["expired_jobs"])
    no_time_count = len(time_analysis["no_time_jobs"])

    if st.button("✅ 确认执行选中的Jobs", type="primary", use_container_width=True):
        if not selected_indices:
            st.error("请至少选择一个Job来执行。")
        else:
            st.session_state.confirming_execution = True
            st.session_state.jobs_to_execute = sorted(list(set(selected_indices)))
            st.rerun()

    # 显示下次部署时间提示
    if time_analysis["future_jobs"]:
        try:
            next_job = min(time_analysis["future_jobs"],
                         key=lambda x: x["job"]["deployment_date"] + " " + x["job"]["time_window"].split('-')[0])
            st.info(f"⏰ 下次部署时间：{next_job['job']['deployment_date']} {next_job['job']['time_window']}")
        except:
            pass

    # 显示警告和提示信息
    if expired_count > 0:
        st.warning(f"⚠️ 注意：有 {expired_count} 个Jobs已过期，请确认是否仍需执行")

    if deployable_count == 0 and future_count == 0 and expired_count == 0 and no_time_count > 0:
        st.info("ℹ️ 所有Jobs都没有时间限制，可随时执行")

def add_test_time_data(original_jobs):
    """添加测试时间数据"""
    from datetime import datetime, timedelta

    now = datetime.now()
    today = now.date()

    # 创建一些有时间信息的测试job
    test_jobs = [
        {
            "deployment_date": today.strftime('%Y-%m-%d'),
            "time_window": f"{(now - timedelta(hours=1)).strftime('%H:%M')}-{(now + timedelta(hours=1)).strftime('%H:%M')}",
            "customer_name": "测试客户A",
            "tenant_name": "test-tenant-a",
            "service_name": "test-service-a",
            "deployment_order": 1,
            "job_name": "test-job-current",
            "parameters": {"test": "current_time"}
        },
        {
            "deployment_date": today.strftime('%Y-%m-%d'),
            "time_window": f"{(now + timedelta(hours=2)).strftime('%H:%M')}-{(now + timedelta(hours=4)).strftime('%H:%M')}",
            "customer_name": "测试客户B",
            "tenant_name": "test-tenant-b",
            "service_name": "test-service-b",
            "deployment_order": 2,
            "job_name": "test-job-future",
            "parameters": {"test": "future_time"}
        },
        {
            "deployment_date": (today - timedelta(days=1)).strftime('%Y-%m-%d'),
            "time_window": "17:00-19:00",
            "customer_name": "测试客户C",
            "tenant_name": "test-tenant-c",
            "service_name": "test-service-c",
            "deployment_order": 3,
            "job_name": "test-job-past",
            "parameters": {"test": "past_time"}
        }
    ]

    # 合并原始jobs和测试jobs
    return original_jobs + test_jobs

def approve_execution(selected_indices: list[int]):
    """确认执行"""
    try:
        result = st.session_state.agent.approve_execution(selected_indices)
        if result["success"]:
            st.success(result["message"])
            st.session_state.execution_status = result["data"]
            st.session_state.deployment_plan["workflow_state"] = "executing"
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"执行确认失败: {str(e)}")

def reject_execution():
    """用户取消执行"""
    st.session_state.confirming_execution = False
    st.session_state.jobs_to_execute = []
    # Optionally, can also reset workflow state if needed
    # st.session_state.deployment_plan["workflow_state"] = "plan_approved"
    st.info("已取消执行确认。")
    time.sleep(1)
    st.rerun()

def display_execution_confirmation():
    """以更美观的方式显示执行确认页面"""
    import time
    st.subheader("🚦 执行前最终确认")
    st.warning("请仔细检查以下将要执行的Jenkins Jobs。一旦开始，无法撤销。")

    indices_to_execute = st.session_state.get('jobs_to_execute', [])
    all_jobs = st.session_state.get('raw_jenkins_jobs', [])
    jobs_to_execute = [all_jobs[i] for i in indices_to_execute if i < len(all_jobs)]

    if not jobs_to_execute:
        st.error("没有选择任何Job来执行。")
        if st.button("返回选择"):
            st.session_state.confirming_execution = False
            st.rerun()
        return

    with st.container(border=True):
        st.markdown("**将要执行的Jobs:**")
        for job in jobs_to_execute:
            st.markdown(f"- **{job.get('job_name', 'N/A')}** ({job.get('customer_name', 'N/A')})")

    st.markdown("---")
    col1, col2, _ = st.columns([1, 1, 2])
    with col1:
        if st.button("✅ 确认执行", type="primary", use_container_width=True):
            with st.status("正在执行选中的Jobs...", expanded=True) as status:
                status.write("调用Agent执行...")
                # The user's block intends to call a different agent method.
                # We follow this intent.
                st.session_state.agent.execute_jenkins_jobs(indices_to_execute)
                status.update(label="执行完成", state="complete")
                st.session_state.confirming_execution = False
                st.success("所有选中的Jobs已开始执行。请在下方查看状态。")
                time.sleep(1) # 短暂延迟以确保状态更新
                st.rerun()

    with col2:
        if st.button("返回修改", use_container_width=True):
            st.session_state.confirming_execution = False
            st.rerun()

def refresh_execution_status():
    """刷新执行状态"""
    try:
        result = st.session_state.agent.get_execution_status()
        if result["success"]:
            st.session_state.execution_status = result["data"]
        else:
            st.error(result["message"])
    except Exception as e:
        st.error(f"刷新状态失败: {str(e)}")

def display_execution_status():
    """以更美观的方式显示执行状态"""
    st.subheader("📊 执行状态监控")

    execution_status = st.session_state.get('execution_status', {})
    if not execution_status:
        st.info("当前没有正在执行的任务。")
        return

    all_finished = all(job.get('status') in ['SUCCESS', 'FAILURE', 'ABORTED'] for job in execution_status.values())
    if all_finished:
        st.success("所有任务已执行完毕！")
        if st.button("返回Job选择"):
            del st.session_state.execution_status
            st.rerun()
        return

    for job_name, status in execution_status.items():
        status_color = {"SUCCESS": "green", "RUNNING": "blue", "FAILURE": "red", "ABORTED": "orange"}.get(status.get('status', '未知'), "gray")
        with st.expander(f":{status_color}[●] **{job_name}** - {status.get('status', '未知')}", expanded=True):
            col1, col2 = st.columns([3, 1])
            with col1:
                st.progress(status.get('progress', 0))
            with col2:
                st.markdown(f"**构建号:** #{status.get('build_number', 'N/A')}")

            if 'log' in status and status['log']:
                with st.container(height=200):
                    st.code(status['log'], language='log')

            if status.get('status') == 'RUNNING':
                if st.button(f"中止 {job_name}", key=f"abort_{job_name}", use_container_width=True):
                    abort_jenkins_job(job_name, status.get('build_number'))
                    st.rerun()
    
    st.markdown("---")
    if not all_finished:
        if st.button("🚨 全部中止", type="primary", use_container_width=True):
            for job_name, status in execution_status.items():
                if status.get('status') == 'RUNNING':
                    abort_jenkins_job(job_name, status.get('build_number'))
            st.rerun()

def abort_jenkins_job(job_name: str, build_number: int):
    """中止Jenkins job"""
    try:
        if 'agent' in st.session_state and st.session_state.agent:
            result = st.session_state.agent.jenkins_client.abort_job(job_name, build_number)
            if result["success"]:
                st.success(result["message"])
            else:
                st.error(result["message"])
        else:
            st.error("Agent未初始化")
    except Exception as e:
        st.error(f"中止Job失败: {str(e)}")

def monitoring_dashboard_tab():
    """实时监控面板标签页"""
    st.header("📊 实时监控面板")
    st.markdown("---")

    if 'monitoring_jobs' not in st.session_state:
        st.session_state.monitoring_jobs = {}

    with st.container(border=True):
        st.subheader("添加新的监控任务")
        col1, col2, col3 = st.columns([2, 1, 1])
        with col1:
            job_name_to_monitor = st.text_input("输入要监控的Jenkins Job名称", label_visibility="collapsed", placeholder="输入要监控的Jenkins Job名称")
        with col2:
            build_number_to_monitor = st.number_input("Build号", min_value=1, value=1, label_visibility="collapsed")
        with col3:
            if st.button("➕ 添加监控", type="primary", use_container_width=True):
                if job_name_to_monitor:
                    with st.status(f"正在启动对 {job_name_to_monitor} Build {build_number_to_monitor} 的监控..."):
                        start_monitoring_job(job_name_to_monitor, build_number_to_monitor)
                else:
                    st.error("请输入Job名称")

    if st.session_state.monitoring_jobs:
        st.markdown("---")
        st.subheader("正在监控的Jobs")
        display_realtime_monitoring()

        if st.button("🔄 刷新所有监控数据", use_container_width=True):
            refresh_monitoring_data()
            st.rerun()

def start_monitoring_job(job_name: str, build_number: int):
    """开始监控Jenkins job"""
    try:
        # 保存监控的job信息
        st.session_state.monitoring_job = {
            "job_name": job_name,
            "build_number": build_number,
            "status": None,
            "console_output": "",
            "stages": None,
            "last_update": None
        }

        # 重置刷新相关的状态
        st.session_state.last_refresh_time = 0
        st.session_state.refresh_counter = 0

        # 立即获取一次数据
        refresh_monitoring_data()
        st.success(f"✅ 开始监控 {job_name} Build {build_number}")

    except Exception as e:
        st.error(f"❌ 开始监控失败: {str(e)}")

def refresh_monitoring_data():
    """刷新监控数据"""
    if not st.session_state.monitoring_job:
        return

    job_name = st.session_state.monitoring_job["job_name"]
    build_number = st.session_state.monitoring_job["build_number"]

    try:
        # 获取job状态
        status_result = st.session_state.agent.jenkins_client.get_build_status(job_name, build_number)

        # 获取控制台输出
        console_result = st.session_state.agent.jenkins_client.get_console_output(job_name, build_number)

        # 获取pipeline stages信息
        stages_result = st.session_state.agent.jenkins_client.get_pipeline_stages(job_name, build_number)

        if status_result["success"]:
            st.session_state.monitoring_job["status"] = status_result

        if console_result["success"]:
            # 只保留最新的20行日志
            console_output = console_result["console_output"]
            if console_output:
                lines = console_output.strip().split('\n')
                last_20_lines = lines[-20:] if len(lines) > 20 else lines
                st.session_state.monitoring_job["console_output"] = '\n'.join(last_20_lines)
            else:
                st.session_state.monitoring_job["console_output"] = "暂无日志输出"

        if stages_result["success"]:
            st.session_state.monitoring_job["stages"] = stages_result

        # 更新时间戳
        import time
        st.session_state.monitoring_job["last_update"] = time.time()

    except Exception as e:
        st.error(f"❌ 刷新数据失败: {str(e)}")

def stop_monitoring(job_name: str = None):
    """停止监控"""
    if job_name and 'monitoring_jobs' in st.session_state:
        # 停止特定job的监控
        if job_name in st.session_state.monitoring_jobs:
            del st.session_state.monitoring_jobs[job_name]
            st.success(f"⏹️ 已停止监控 {job_name}")
    else:
        # 停止所有监控
        st.session_state.monitoring_job = None
        st.session_state.monitoring_jobs = {}
        st.session_state.auto_refresh = False
        st.session_state.last_refresh_time = 0
        st.session_state.refresh_counter = 0
        st.success("⏹️ 已停止所有监控")

def display_realtime_monitoring():
    """以更美观的方式显示实时监控数据"""
    for job_name, data in list(st.session_state.monitoring_jobs.items()):
        status = data.get('status', '未知')
        status_color = {"SUCCESS": "green", "RUNNING": "blue", "FAILURE": "red", "ABORTED": "orange"}.get(status, "gray")
        
        with st.expander(f":{status_color}[●] **{job_name}** - Build #{data.get('build_number', 'N/A')}", expanded=True):
            col1, col2, col3 = st.columns(3)
            col1.metric("状态", status)
            col2.metric("持续时间", f"{data.get('duration', 0)}s")
            col3.metric("预计剩余", f"{data.get('estimated_duration', 0)}s")

            st.progress(data.get('progress', 0))

            if 'console_output' in data and data['console_output']:
                with st.container(height=300):
                    st.code(data['console_output'], language='log')

            if st.button(f"停止监控 {job_name}", key=f"stop_{job_name}", use_container_width=True):
                with st.status(f"正在停止对 {job_name} 的监控..."):
                    stop_monitoring(job_name)
                st.rerun()

def workflow_page():
    st.header("🚀 端到端工作流")
    st.markdown("---")

    if 'workflow_running' not in st.session_state:
        st.session_state.workflow_running = False
    if 'workflow_status' not in st.session_state:
        st.session_state.workflow_status = []

    with st.container(border=True):
        st.subheader("工作流配置")
        col1, col2, col3 = st.columns(3)
        with col1:
            version = st.text_input("版本号", "1.0.0")
        with col2:
            environment = st.selectbox("环境", ["staging", "production"])
        with col3:
            # This should be dynamically loaded
            llm_provider = st.selectbox("LLM提供商", ["OpenAI", "Azure"], key="workflow_llm_provider")

        if not st.session_state.workflow_running:
            if st.button("🚀 启动工作流", type="primary", use_container_width=True):
                with st.status("正在启动端到端工作流..."):
                    st.session_state.workflow_running = True
                    st.session_state.workflow_status = []
                st.rerun()
        else:
            if st.button("⏹️ 中止工作流", use_container_width=True):
                st.session_state.workflow_running = False
                st.session_state.workflow_status.append(("工作流被用户中止", "warning"))
                st.rerun()

    if st.session_state.workflow_running:
        run_workflow(version, environment, llm_provider)

    if st.session_state.workflow_status:
        st.markdown("---")
        st.subheader("工作流状态")
        display_workflow_status()

def display_workflow_status():
    """以更美观的方式显示工作流状态"""
    for status, msg_type in st.session_state.workflow_status:
        if msg_type == "success":
            st.success(status)
        elif msg_type == "error":
            st.error(status)
        elif msg_type == "info":
            st.info(status)
        elif msg_type == "warning":
            st.warning(status)

def run_workflow(version, environment, llm_provider):
    # 这是一个模拟函数，实际应用中会调用后端逻辑
    import time

    steps = [
        ("生成发布计划", lambda: time.sleep(2)),
        ("等待审批", lambda: time.sleep(1)),
        ("执行部署", lambda: time.sleep(3)),
        ("监控部署状态", lambda: time.sleep(2)),
    ]

    # 确保在开始时不重复添加起始信息
    if not st.session_state.workflow_status:
        st.session_state.workflow_status.append((f"[阶段 1/{len(steps)}] {steps[0][0]}...", "info"))
        st.rerun()

    current_step_index = sum(1 for s, t in st.session_state.workflow_status if t == 'success')

    if current_step_index < len(steps):
        step_name, step_func = steps[current_step_index]
        
        # 模拟执行
        step_func()
        
        # 更新当前步骤状态
        st.session_state.workflow_status[-1] = (f"✅ [阶段 {current_step_index + 1}/{len(steps)}] {step_name} 完成", "success")

        # 如果不是最后一步，添加下一步的提示
        if current_step_index + 1 < len(steps):
            next_step_name = steps[current_step_index + 1][0]
            st.session_state.workflow_status.append((f"[阶段 {current_step_index + 2}/{len(steps)}] {next_step_name}...", "info"))
        else:
            # 所有步骤完成
            st.session_state.workflow_status.append(("🎉 工作流所有阶段执行完毕!", "success"))
            st.session_state.workflow_running = False
            st.balloons()

        st.rerun()