import streamlit as st
from src.logic.llm_utils import llm_config_manager, LLMProvider

def display_sidebar():
    """显示侧边栏"""
    with st.sidebar:
        st.image("https://www.inf-it.com/wp-content/uploads/2020/08/cropped-logo_inf-it_header.png", width=200)
        st.title("Release Helper")
        st.caption("v2.0 Modern")

        # LLM 配置
        with st.expander("🤖 LLM 配置", expanded=True):
            display_llm_config()

        # 环境配置检查
        with st.expander("✅ 环境检查"):
            display_env_check()

        # 工作流进度
        with st.expander("📊 工作流进度"):
            display_workflow_progress_sidebar()

        st.divider()

        # 重置会话
        if st.button("🔄 重置会话状态", use_container_width=True):
            reset_session_state()
            st.success("会话状态已重置")
            st.rerun()

def display_env_check():
    """显示环境配置检查"""
    st.info("所有环境配置正常。")
    # 在这里可以添加更详细的检查，例如：
    # st.metric("数据库连接", "✅ 正常")
    # st.metric("Jenkins连接", "✅ 正常")

def display_llm_config():
    """显示LLM配置"""
    providers = llm_config_manager.get_available_providers()
    
    # 获取当前配置
    current_config = llm_config_manager.get_current_config()
    current_provider_value = current_config['provider']
    current_model_value = current_config['model']

    # 获取当前provider在列表中的索引
    provider_values = [p['value'] for p in providers]
    try:
        current_provider_index = provider_values.index(current_provider_value)
    except ValueError:
        current_provider_index = 0

    selected_provider = st.selectbox(
        "选择LLM供应商",
        options=providers,
        index=current_provider_index,
        format_func=lambda p: f"{p['label']} {p['status']}"
    )

    if selected_provider:
        provider_enum = LLMProvider(selected_provider['value'])
        models = llm_config_manager.get_available_models(provider_enum)
        model_names = [m['value'] for m in models]

        try:
            current_model_index = model_names.index(current_model_value)
        except ValueError:
            current_model_index = 0

        selected_model = st.selectbox(
            "选择模型",
            options=model_names,
            index=current_model_index,
            format_func=lambda m: llm_config_manager._format_model_name(m)
        )

        if st.button("应用配置", use_container_width=True):
            llm_config_manager.set_current_config(selected_provider['value'], selected_model)
            st.success(f"LLM配置已更新为: {selected_provider['label']} - {selected_model}")

def display_workflow_progress_sidebar():
    """显示工作流进度"""
    # 这是一个示例，您可以根据实际的会话状态来更新
    steps = ["计划", "审批", "执行", "监控"]
    current_step = st.session_state.get('workflow_step', '计划')

    # 使用指标显示当前步骤
    st.metric("当前阶段", current_step)

    # 使用进度条显示
    try:
        current_step_index = steps.index(current_step)
        progress_percent = (current_step_index + 1) / len(steps)
        st.progress(progress_percent, text=f"进度: {current_step_index + 1}/{len(steps)}")
    except ValueError:
        st.progress(0, text="未开始")

def reset_session_state():
    """重置会话状态"""
    for key in list(st.session_state.keys()):
        del st.session_state[key]