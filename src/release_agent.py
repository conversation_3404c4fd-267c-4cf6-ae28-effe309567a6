"""
Agent核心逻辑实现
实现基于release-helper.md的智能发布计划助手逻辑
"""

import re
from typing import Dict, Any, List, Optional, Tuple
import logging
from pathlib import Path

from .agent_workflow import ReleaseAgentWorkflow, WorkflowState
from .database import ReleaseQueryService
from .jenkins_client import Jenkins<PERSON><PERSON>, JenkinsJobGenerator
from .logic.llm_utils import llm_config_manager
from .utils import parse_version_environment
from .constants import ERROR_MESSAGES, SUCCESS_MESSAGES

logger = logging.getLogger(__name__)

class ReleaseAgent:
    """智能发布计划助手"""
    
    def __init__(self, prompt_file_path: Optional[str] = None):
        self.workflow = ReleaseAgentWorkflow()
        self.query_service = ReleaseQueryService()
        self.jenkins_client = JenkinsClient()
        self.job_generator = JenkinsJobGenerator()
        
        # 加载提示词文件
        self.prompt_content = self._load_prompt_file(prompt_file_path)
        
        # 当前会话状态
        self.current_state: Optional[WorkflowState] = None
    
    def _load_prompt_file(self, file_path: Optional[str]) -> str:
        """加载提示词文件"""
        if not file_path:
            # 默认使用release-helper.md
            file_path = "release-helper.md"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"成功加载提示词文件: {file_path}")
            return content
        except Exception as e:
            logger.warning(f"加载提示词文件失败: {e}")
            return ""
    
    def parse_user_input(self, user_input: str) -> Tuple[Optional[str], Optional[str]]:
        """解析用户输入，提取版本号和环境名"""
        # 首先尝试正则表达式解析
        version, environment = parse_version_environment(user_input)

        if version and environment:
            return version, environment

        # 如果正则解析失败，尝试使用LLM智能解析
        try:
            return self._llm_parse(user_input)
        except Exception as e:
            logger.warning(f"LLM解析失败: {e}")
            return None, None

    def _llm_parse(self, user_input: str) -> Tuple[Optional[str], Optional[str]]:
        """使用LLM智能解析用户输入"""
        try:
            # 检查是否有可用的LLM配置
            current_config = llm_config_manager.get_current_config()
            api_key = llm_config_manager.get_api_key(llm_config_manager.current_provider)

            if not api_key:
                logger.info("未配置LLM API密钥，跳过智能解析")
                return None, None

            # 创建LLM实例
            llm = llm_config_manager.create_llm_instance(temperature=0.1, max_tokens=100)

            # 构建解析提示词
            prompt = f"""
请从以下用户输入中提取版本号和环境名：

用户输入："{user_input}"

版本号格式通常为：25R1.2, 24R3.0 等（数字+R+数字.数字）
环境名通常为：Prod, Production, Test, Testing, Staging, Dev, Development 等

请只返回JSON格式的结果，如：
{{"version": "25R1.2", "environment": "Prod"}}

如果无法提取，请返回：
{{"version": null, "environment": null}}
"""

            from langchain_core.messages import HumanMessage
            response = llm.invoke([HumanMessage(content=prompt)])

            # 解析LLM响应
            import json
            response_text = response.content if hasattr(response, 'content') else str(response)

            # 尝试提取JSON
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1

            if json_start >= 0 and json_end > json_start:
                json_str = response_text[json_start:json_end]
                result = json.loads(json_str)

                version = result.get('version')
                environment = result.get('environment')

                if version and environment:
                    logger.info(f"LLM智能解析成功: {version}, {environment}")
                    return version, environment

            return None, None

        except Exception as e:
            logger.error(f"LLM解析过程中出错: {e}")
            return None, None
    
    def process_user_request(self, user_input: str) -> Dict[str, Any]:
        """处理用户请求"""
        logger.info(f"处理用户请求: {user_input}")
        
        # 解析用户输入
        version, environment = self.parse_user_input(user_input)
        
        if not version or not environment:
            return {
                "success": False,
                "message": ERROR_MESSAGES["NO_VERSION_ENV"],
                "data": None
            }
        
        try:
            # 启动工作流
            self.current_state = self.workflow.run(version, environment)
            
            if self.current_state.errors:
                return {
                    "success": False,
                    "message": "; ".join(self.current_state.errors),
                    "data": None
                }
            
            # 格式化发布计划
            formatted_plan = self._format_deployment_plan(
                self.current_state.deployment_plan, version, environment
            )
            
            # 格式化Jenkins jobs
            formatted_jobs = self._format_jenkins_jobs(self.current_state.jenkins_jobs)
            
            return {
                "success": True,
                "message": f"成功获取 {version} {environment}环境 的发布计划",
                "data": {
                    "version": version,
                    "environment": environment,
                    "deployment_plan": formatted_plan,
                    "jenkins_jobs": formatted_jobs,
                    "raw_jenkins_jobs": self.current_state.jenkins_jobs, # 返回原始jobs
                    "workflow_state": self.current_state.current_step
                }
            }
            
        except Exception as e:
            error_msg = f"处理请求失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }
    
    def _format_deployment_plan(self, deployment_plan: Optional[List[Dict]], 
                              version: str, environment: str) -> str:
        """格式化发布计划为表格"""
        if not deployment_plan:
            return f"未找到 {version} 在 {environment} 环境的发布计划。"
        
        # 创建表格
        table_lines = [
            f"#### {version} {environment}环境 发布计划",
            "",
            "| 计划部署日期 | 时间窗口 | 客户名 | 租户名 | Service名 | 是否部署PS代码 |",
            "| :--- | :--- | :--- | :--- | :--- | :--- |"
        ]
        
        for item in deployment_plan:
            row = f"| {item.get('计划部署日期', '')} | {item.get('时间窗口', '')} | " \
                  f"{item.get('客户名', '')} | {item.get('租户名', '')} | " \
                  f"{item.get('Service名', '')} | {item.get('是否部署PS代码', '')} |"
            table_lines.append(row)
        
        return "\n".join(table_lines)
    
    def _format_jenkins_jobs(self, jenkins_jobs: Optional[List[Dict]]) -> str:
        """格式化Jenkins jobs列表"""
        if not jenkins_jobs:
            return "没有可执行的Jenkins jobs。"
        
        job_lines = ["#### 要执行的Jenkins Job列表", ""]
        
        for i, job in enumerate(jenkins_jobs, 1):
            job_lines.extend([
                f"**{i}. {job['deployment_date']} | {job['time_window']} | "
                f"{job['customer_name']} | {job['tenant_name']} | {job['service_name']}**",
                f"Jenkins job: `{job['job_name']}`",
                "传入参数："
            ])
            
            for key, value in job['parameters'].items():
                job_lines.append(f"  - {key}: {value}")
            
            job_lines.append("")  # 空行分隔
        
        return "\n".join(job_lines)
    
    def approve_deployment_plan(self) -> Dict[str, Any]:
        """用户审批发布计划"""
        if not self.current_state:
            return {
                "success": False,
                "message": "没有活动的工作流状态",
                "data": None
            }
        
        try:
            self.current_state = self.workflow.approve_plan(self.current_state)
            return {
                "success": True,
                "message": "发布计划已审批，请确认是否执行Jenkins jobs",
                "data": {
                    "workflow_state": self.current_state.current_step,
                    "jenkins_jobs": self.current_state.jenkins_jobs
                }
            }
        except Exception as e:
            error_msg = f"审批失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }
    
    def reject_deployment_plan(self, reason: str = "") -> Dict[str, Any]:
        """用户拒绝发布计划"""
        if not self.current_state:
            return {
                "success": False,
                "message": "没有活动的工作流状态",
                "data": None
            }
        
        try:
            self.current_state = self.workflow.reject_plan(self.current_state, reason)
            return {
                "success": True,
                "message": f"发布计划已拒绝: {reason}",
                "data": {
                    "workflow_state": self.current_state.current_step
                }
            }
        except Exception as e:
            error_msg = f"拒绝失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }
    
    def approve_execution(self, selected_job_indices: Optional[List[int]] = None) -> Dict[str, Any]:
        """用户确认执行Jenkins jobs"""
        if not self.current_state:
            return {
                "success": False,
                "message": "没有活动的工作流状态",
                "data": None
            }
        
        try:
            selected_jobs = None
            if selected_job_indices and self.current_state.jenkins_jobs:
                selected_jobs = [
                    self.current_state.jenkins_jobs[i] 
                    for i in selected_job_indices 
                    if 0 <= i < len(self.current_state.jenkins_jobs)
                ]
            
            self.current_state = self.workflow.approve_execution(self.current_state, selected_jobs)
            
            if not selected_jobs:
                return {
                    "success": False,
                    "message": "没有选择任何有效的Job来执行。",
                    "data": None
                }

            return {
                "success": True,
                "message": "开始执行Jenkins jobs",
                "data": {
                    "workflow_state": self.current_state.current_step,
                    "selected_jobs": selected_jobs
                }
            }
        except Exception as e:
            error_msg = f"执行确认失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }
    
    def reject_execution(self, reason: str = "") -> Dict[str, Any]:
        """用户拒绝执行"""
        if not self.current_state:
            return {
                "success": False,
                "message": "没有活动的工作流状态",
                "data": None
            }
        
        try:
            self.current_state = self.workflow.reject_execution(self.current_state, reason)
            return {
                "success": True,
                "message": f"执行已取消: {reason}",
                "data": {
                    "workflow_state": self.current_state.current_step
                }
            }
        except Exception as e:
            error_msg = f"取消执行失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }

    def execute_jenkins_jobs(self, selected_job_indices: List[int]) -> Dict[str, Any]:
        """执行选中的Jenkins jobs"""
        try:
            if not self.current_state or not self.current_state.jenkins_jobs:
                return {
                    "success": False,
                    "message": "没有可执行的Jenkins jobs",
                    "data": None
                }

            selected_jobs = [
                self.current_state.jenkins_jobs[i]
                for i in selected_job_indices
                if 0 <= i < len(self.current_state.jenkins_jobs)
            ]

            if not selected_jobs:
                return {
                    "success": False,
                    "message": "没有选择有效的Job来执行",
                    "data": None
                }

            # 更新工作流状态
            self.current_state = self.workflow.approve_execution(self.current_state, selected_jobs)

            return {
                "success": True,
                "message": f"开始执行 {len(selected_jobs)} 个Jenkins jobs",
                "data": {
                    "selected_jobs": selected_jobs,
                    "workflow_state": self.current_state.current_step
                }
            }

        except Exception as e:
            error_msg = f"执行Jenkins jobs失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }


    def monitor_jenkins_job(self, job_name: str, build_number: int) -> Dict[str, Any]:
        """监控特定Jenkins job的执行"""
        try:
            status_info = self.jenkins_client.get_build_status(job_name, build_number)
            console_info = self.jenkins_client.get_console_output(job_name, build_number)
            
            return {
                "success": True,
                "message": f"Job {job_name} Build {build_number} 状态",
                "data": {
                    "status": status_info,
                    "console_output": console_info.get("console_output", "")
                }
            }
        except Exception as e:
            error_msg = f"监控Job失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }

    def get_execution_status(self) -> Dict[str, Any]:
        """获取当前执行状态"""
        try:
            if not self.current_state:
                return {
                    "success": False,
                    "message": "没有正在执行的工作流",
                    "data": None
                }

            # 构建执行状态数据
            status_data = {
                "workflow_state": self.current_state.current_step,
                "current_job_index": self.current_state.current_job_index,
                "execution_completed": self.current_state.execution_completed,
                "waiting_for_completion": self.current_state.waiting_for_completion,
                "current_executing_job": self.current_state.current_executing_job,
                "current_build_number": self.current_state.current_build_number,
                "jenkins_jobs": self.current_state.jenkins_jobs or [],
                "job_results": self.current_state.job_results,
                "messages": self.current_state.messages,
                "errors": self.current_state.errors
            }

            return {
                "success": True,
                "message": "获取执行状态成功",
                "data": status_data
            }

        except Exception as e:
            error_msg = f"获取执行状态失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": None
            }
