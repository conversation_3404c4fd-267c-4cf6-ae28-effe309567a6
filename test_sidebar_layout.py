#!/usr/bin/env python3
"""
侧边栏布局测试应用
测试新的工作流进度显示和LLM配置折叠
"""

import streamlit as st
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.sidebar import display_sidebar
from src.workflow_manager import workflow_manager

def simulate_workflow_states():
    """模拟不同的工作流状态"""
    st.subheader("🎮 工作流状态模拟器")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**选择工作流步骤:**")
        selected_step = st.selectbox(
            "当前步骤",
            options=[step["key"] for step in workflow_manager.WORKFLOW_STEPS],
            format_func=lambda x: f"{workflow_manager.get_step_info(x)['icon']} {workflow_manager.get_step_info(x)['name']}",
            index=0
        )
        
        if st.button("设置为当前步骤"):
            workflow_manager.update_workflow_step(selected_step)
            st.success(f"已设置当前步骤为: {workflow_manager.get_step_info(selected_step)['name']}")
            st.rerun()
    
    with col2:
        st.write("**模拟数据:**")
        
        # 模拟部署计划
        if st.button("生成部署计划"):
            st.session_state.deployment_plan = {
                "workflow_state": "plan_generated",
                "version": "25R1.2",
                "environment": "Prod",
                "jobs": [
                    {"job_name": "deploy-service-a", "customer": "Customer1"},
                    {"job_name": "deploy-service-b", "customer": "Customer2"}
                ]
            }
            st.success("已生成模拟部署计划")
            st.rerun()
        
        # 模拟执行状态
        if st.button("模拟执行状态"):
            st.session_state.execution_status = {
                "deploy-service-a": {"status": "SUCCESS", "build_number": 123},
                "deploy-service-b": {"status": "RUNNING", "build_number": 124},
                "deploy-service-c": {"status": "FAILURE", "build_number": 125},
                "deploy-service-d": {"status": "PENDING", "build_number": None}
            }
            st.success("已生成模拟执行状态")
            st.rerun()
        
        # 清除状态
        if st.button("清除所有状态"):
            workflow_manager.reset_workflow()
            st.success("已清除所有工作流状态")
            st.rerun()

def display_current_state():
    """显示当前状态信息"""
    st.subheader("📊 当前状态信息")
    
    # 显示当前步骤
    current_step = workflow_manager.get_current_step()
    step_info = workflow_manager.get_step_info(current_step)
    st.info(f"当前步骤: {step_info['icon']} {step_info['name']}")
    
    # 显示会话状态
    with st.expander("🔍 会话状态详情", expanded=False):
        st.write("**工作流相关状态:**")
        workflow_keys = [
            'workflow_step', 'deployment_plan', 'execution_status', 
            'workflow_history', 'raw_jenkins_jobs'
        ]
        
        for key in workflow_keys:
            if key in st.session_state:
                st.write(f"- **{key}**: ✅ 存在")
                if key == 'workflow_history':
                    history = workflow_manager.get_workflow_history()
                    if history:
                        st.write("  工作流历史:")
                        for entry in history[-3:]:  # 显示最近3条
                            st.write(f"    - {entry['step_name']} ({entry['timestamp'].strftime('%H:%M:%S')})")
            else:
                st.write(f"- **{key}**: ❌ 不存在")

def main():
    """主函数"""
    st.set_page_config(
        page_title="侧边栏布局测试",
        page_icon="🎨",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 显示侧边栏
    display_sidebar()
    
    # 主内容区域
    st.title("🎨 侧边栏布局测试")
    st.markdown("---")
    
    st.markdown("""
    ## 📋 测试说明
    
    这个页面用于测试新的侧边栏布局，重点关注：
    
    ### 🎯 主要改进：
    
    1. **工作流进度突出显示** - 不再折叠，使用更醒目的样式
    2. **LLM配置默认折叠** - 减少视觉干扰
    3. **更好的进度可视化** - 包括步骤状态和执行统计
    4. **智能状态检测** - 根据实际数据自动判断当前步骤
    
    ### 🔍 检查要点：
    
    - ✅ 工作流进度是否突出显示在顶部
    - ✅ LLM配置是否默认折叠
    - ✅ 进度条和状态指示是否清晰
    - ✅ 步骤切换是否流畅
    - ✅ 执行状态统计是否准确
    """)
    
    # 工作流状态模拟器
    simulate_workflow_states()
    
    st.markdown("---")
    
    # 显示当前状态
    display_current_state()
    
    # 使用说明
    st.markdown("---")
    st.subheader("📖 使用说明")
    
    st.markdown("""
    ### 🎮 如何测试：
    
    1. **查看默认状态** - 观察侧边栏的初始布局
    2. **生成部署计划** - 点击"生成部署计划"按钮，观察步骤变化
    3. **模拟执行状态** - 点击"模拟执行状态"按钮，观察执行统计
    4. **切换步骤** - 手动选择不同步骤，观察显示效果
    5. **重置状态** - 使用重置功能清除所有状态
    
    ### 🎨 布局特点：
    
    - **工作流进度**: 使用渐变背景和阴影效果，更加突出
    - **步骤列表**: 清晰的图标和状态指示
    - **执行统计**: 紧凑的三列布局显示成功/运行/失败数量
    - **下一步建议**: 智能提示下一步操作
    - **配置折叠**: LLM配置默认折叠，减少干扰
    """)

if __name__ == "__main__":
    main()
