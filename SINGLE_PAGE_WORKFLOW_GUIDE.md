# 单页面工作流使用指南

## 📋 功能概述

新的单页面工作流将整个发布流程集成在一个页面中，用户可以按照流的形式连续完成所有步骤，无需在不同标签页之间切换。

## 🌊 工作流设计理念

### 核心特点
1. **连续性** - 所有步骤在同一页面按顺序展示
2. **直观性** - 清晰的视觉指示当前步骤和进度
3. **流畅性** - 每个步骤完成后自动展示下一步
4. **实时性** - 实时监控和状态更新

### 设计原则
- **渐进式展示** - 只有当前步骤可操作，已完成步骤显示结果
- **状态驱动** - 根据实际数据状态自动判断当前步骤
- **用户确认** - 关键操作需要用户明确确认
- **实时反馈** - 提供即时的状态反馈和进度显示

## 🚀 完整工作流程

### 步骤1: 计划查询 📝
**目标:** 查询和生成发布计划

**操作流程:**
1. 输入版本号和环境，或使用自然语言描述
2. 点击"🔍 查询发布计划"或"🧪 使用测试数据"
3. 系统解析查询并生成发布计划
4. 自动进入下一步骤

**输入示例:**
- 版本号: `25R1.2`
- 环境: `Prod`
- 自然语言: `列出 25R1.2 Prod 的发布计划`

### 步骤2: 计划审批 ✅
**目标:** 审核和确认发布计划

**显示内容:**
- 版本、环境、计划数量等基本信息
- 详细的部署计划表格
- 对应的Jenkins Jobs列表

**操作选择:**
- ✅ **确认计划正确** - 进入执行阶段
- ❌ **重新查询** - 返回步骤1重新查询

### 步骤3: 执行管理 🚀
**目标:** 选择和执行Jenkins Jobs

**功能特性:**
- **时间窗口分析** - 自动分析Jobs的部署时间
- **智能分类** - 将Jobs分为可部署/未到时间/已过期
- **多选功能** - 支持选择多个Jobs同时执行
- **时间提示** - 显示每个Job的部署时间窗口

**操作流程:**
1. 查看时间窗口分析结果
2. 选择要执行的Jenkins Jobs
3. 点击"🚀 确认执行选中的Jobs"
4. 进入执行确认步骤

### 步骤4: 执行确认 🔍
**目标:** 确认Jenkins Job参数和执行顺序

**显示内容:**
- **执行顺序** - 按选择顺序显示Jobs
- **基本信息** - 客户、服务、部署时间
- **Jenkins参数** - 详细的Job参数列表
- **安全提示** - 提醒用户仔细检查信息

**操作选择:**
- ✅ **确认执行** - 开始实际执行
- ❌ **取消执行** - 返回步骤3重新选择

### 步骤5: 状态监控 📊
**目标:** 监控执行状态和Console输出

**监控功能:**
- **实时状态** - 显示每个Job的执行状态
- **进度统计** - 总体进度和成功/失败统计
- **Console输出** - 显示最新20行日志
- **自动刷新** - 可配置的自动刷新间隔

**状态类型:**
- 🟡 **PENDING** - 等待执行
- 🔵 **RUNNING** - 正在执行
- 🟢 **SUCCESS** - 执行成功
- 🔴 **FAILURE** - 执行失败
- 🟠 **ABORTED** - 已中止

## 🎨 视觉设计特点

### 1. 步骤导航
- **已完成步骤** - 绿色背景 + ✅ 图标
- **当前步骤** - 渐变紫色背景 + 脉冲动画
- **未开始步骤** - 灰色虚线边框 + ⏳ 图标

### 2. 进度指示
- **整体进度条** - 显示工作流完成百分比
- **步骤标题** - 突出显示当前步骤
- **状态卡片** - 美观的渐变背景和阴影

### 3. 交互反馈
- **按钮状态** - 主要操作使用primary样式
- **状态颜色** - 不同状态使用不同颜色
- **动画效果** - 当前步骤有脉冲动画

## 🔧 技术实现

### 核心组件
1. **WorkflowManager** - 工作流状态管理
2. **单页面组件** - 所有步骤的UI组件
3. **状态检测** - 智能判断当前步骤
4. **实时监控** - Jenkins状态和日志获取

### 状态管理
```python
# 主要会话状态
- deployment_plan: 发布计划数据
- selected_jobs_for_execution: 选中的Jobs
- execution_status: 执行状态
- show_execution_confirmation: 是否显示确认页面
```

### 自动刷新机制
- 支持5/10/15/30秒刷新间隔
- 只在有运行中的Jobs时自动刷新
- 手动刷新按钮随时可用

## 📱 使用体验

### 优势
1. **一站式体验** - 无需切换标签页
2. **流程清晰** - 步骤顺序和进度一目了然
3. **操作简单** - 每步只需要必要的操作
4. **实时反馈** - 即时的状态更新和进度显示

### 适用场景
- **标准发布流程** - 按固定步骤进行的发布
- **快速部署** - 需要快速完成的紧急发布
- **批量操作** - 同时部署多个服务
- **监控需求** - 需要实时监控执行状态

## 🔍 测试和验证

### 测试应用
- `test_single_workflow.py` - 专门的测试应用
- 支持模拟数据和真实Jenkins集成
- 完整的工作流测试覆盖

### 测试场景
1. **完整流程** - 从查询到监控的完整测试
2. **错误处理** - 各种异常情况的处理
3. **状态切换** - 步骤间的状态转换
4. **实时更新** - 监控页面的实时刷新

## 💡 使用建议

### 最佳实践
1. **仔细审查** - 在执行确认步骤仔细检查所有参数
2. **时间规划** - 注意Jobs的时间窗口限制
3. **监控关注** - 执行期间密切关注状态变化
4. **及时处理** - 发现问题及时停止或重试

### 注意事项
1. **网络连接** - 确保与Jenkins的网络连接稳定
2. **权限检查** - 确认有足够的Jenkins执行权限
3. **资源监控** - 注意目标环境的资源使用情况
4. **备份准备** - 重要发布前确保有回滚方案

## 🎉 总结

单页面工作流提供了一个完整、直观、高效的发布管理体验。通过将所有步骤集成在一个页面中，用户可以：

- 🚀 **快速完成** 整个发布流程
- 👀 **清晰了解** 当前进度和状态
- 🔄 **实时监控** 执行过程和结果
- 🎯 **专注操作** 无需分心切换页面

这种设计特别适合需要频繁发布、对效率要求高的团队使用。
