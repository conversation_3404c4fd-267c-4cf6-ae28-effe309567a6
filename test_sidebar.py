#!/usr/bin/env python3
"""
侧边栏测试应用
测试配置状态显示功能
"""

import streamlit as st
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.sidebar import display_sidebar

def main():
    """主函数"""
    st.set_page_config(
        page_title="配置状态测试",
        page_icon="🔧",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 显示侧边栏
    display_sidebar()
    
    # 主内容区域
    st.title("🔧 配置状态测试")
    st.markdown("---")
    
    st.markdown("""
    ## 📋 测试说明
    
    这个页面用于测试侧边栏的配置状态显示功能。
    
    ### 🔍 检查项目：
    
    1. **配置状态总览** - 显示所有配置的整体状态
    2. **数据库配置** - 显示MySQL数据库连接配置和状态
    3. **Jenkins配置** - 显示Jenkins服务器连接配置和状态  
    4. **LLM配置** - 显示各个LLM供应商的API密钥配置状态
    5. **工作流进度** - 显示当前工作流的执行进度
    
    ### 📊 状态说明：
    
    - ✅ **正常** - 配置完整且连接测试成功
    - ⚠️ **警告** - 配置存在但可能有问题
    - ❌ **错误** - 配置缺失或连接失败
    
    ### 🔄 刷新功能：
    
    点击侧边栏顶部的"🔄 刷新配置状态"按钮可以重新检测所有配置状态。
    """)
    
    # 显示当前环境变量状态（用于调试）
    with st.expander("🐛 调试信息", expanded=False):
        st.write("**当前环境变量:**")
        
        env_vars = [
            'MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER', 'MYSQL_DATABASE',
            'JENKINS_URL', 'JENKINS_USERNAME', 
            'GOOGLE_API_KEY', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY',
            'DEFAULT_LLM_PROVIDER', 'DEFAULT_LLM_MODEL'
        ]
        
        for var in env_vars:
            value = os.getenv(var)
            if value:
                if 'PASSWORD' in var or 'TOKEN' in var or 'API_KEY' in var:
                    display_value = "***"
                else:
                    display_value = value
                st.write(f"- **{var}**: `{display_value}`")
            else:
                st.write(f"- **{var}**: ❌ 未设置")

if __name__ == "__main__":
    main()
