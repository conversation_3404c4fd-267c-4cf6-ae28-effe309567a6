#!/usr/bin/env python3
"""
测试脚本 - 验证代码修复是否有效
"""

import sys
import os
import importlib.util

def test_imports():
    """测试所有模块的导入是否正常"""
    print("🧪 测试模块导入...")
    
    modules_to_test = [
        'src.constants',
        'src.utils', 
        'src.database',
        'src.jenkins_client',
        'src.release_agent',
        'src.agent_workflow',
        'src.logic.llm_utils',
        'src.ui.components',
        'src.ui.sidebar',
        'src.ui.tabs'
    ]
    
    failed_imports = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            failed_imports.append(module_name)
        except Exception as e:
            print(f"⚠️  {module_name}: {e}")
            failed_imports.append(module_name)
    
    return len(failed_imports) == 0

def test_constants():
    """测试常量模块"""
    print("\n🧪 测试常量模块...")
    
    try:
        from src.constants import (
            APP_TITLE, APP_ICON, APP_CAPTION,
            WORKFLOW_STATES, JOB_STATUS, STATUS_COLORS,
            ERROR_MESSAGES, SUCCESS_MESSAGES, TAB_TITLES
        )
        
        # 验证基本常量
        assert isinstance(APP_TITLE, str)
        assert isinstance(TAB_TITLES, list)
        assert len(TAB_TITLES) == 5
        
        # 验证状态映射
        assert 'SUCCESS' in STATUS_COLORS
        assert 'RUNNING' in STATUS_COLORS
        
        print("✅ 常量模块测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 常量模块测试失败: {e}")
        return False

def test_utils():
    """测试工具函数模块"""
    print("\n🧪 测试工具函数模块...")
    
    try:
        from src.utils import (
            parse_version_environment,
            format_time_diff,
            get_status_color,
            generate_release_branch,
            generate_release_tag
        )
        from datetime import timedelta
        
        # 测试版本解析
        version, env = parse_version_environment("列出 25R1.2 Prod 的发布计划")
        assert version == "25R1.2"
        assert env == "Prod"
        
        # 测试时间格式化
        time_str = format_time_diff(timedelta(hours=2, minutes=30))
        assert "2小时30分钟" == time_str
        
        # 测试状态颜色
        color = get_status_color("SUCCESS")
        assert color == "green"
        
        # 测试版本生成
        branch = generate_release_branch("25R1.2")
        assert branch == "release/251.2"
        
        tag = generate_release_tag("25R1.2")
        assert tag == "LR/251.2.0"
        
        print("✅ 工具函数模块测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 工具函数模块测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接（不实际连接）"""
    print("\n🧪 测试数据库模块...")
    
    try:
        from src.database import DatabaseManager, ReleaseQueryService
        
        # 只测试类是否可以实例化（不实际连接数据库）
        print("✅ 数据库模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库模块测试失败: {e}")
        return False

def test_jenkins_client():
    """测试Jenkins客户端（不实际连接）"""
    print("\n🧪 测试Jenkins客户端模块...")
    
    try:
        from src.jenkins_client import JenkinsJobGenerator
        
        # 测试job生成器
        generator = JenkinsJobGenerator()
        
        # 测试生成Jenkins jobs（使用空数据）
        jobs = generator.generate_jenkins_jobs([], "25R1.2")
        assert isinstance(jobs, list)
        
        print("✅ Jenkins客户端模块测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Jenkins客户端模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始代码修复验证测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_constants,
        test_utils,
        test_database_connection,
        test_jenkins_client
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！代码修复成功。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
