import streamlit as st
from src.ui.sidebar import display_sidebar
from src.ui.tabs import plan_query_tab, plan_approval_tab, execution_management_tab, monitoring_dashboard_tab, workflow_page
from src.ui.config_status import display_full_config_status
from src.constants import APP_TITLE, APP_ICON, APP_CAPTION, TAB_TITLES

def load_custom_css():
    """加载自定义CSS文件"""
    try:
        with open("src/ui/style.css", "r") as f:
            st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.warning("自定义样式文件 'src/ui/style.css' 未找到.")

def initialize_session_state():
    """Initialize session state variables."""
    if 'current_view' not in st.session_state:
        st.session_state.current_view = 'main'

def main():
    """Main function to run the Streamlit app."""
    st.set_page_config(
        page_title=APP_TITLE,
        page_icon=APP_ICON,
        layout="wide",
        initial_sidebar_state="expanded"
    )

    load_custom_css()
    initialize_session_state()

    display_sidebar()

    st.title("智能发布助手")
    st.caption(APP_CAPTION)

    # 添加配置状态标签
    extended_tab_titles = TAB_TITLES + ["⚙️ 配置"]
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs(extended_tab_titles)

    with tab1:
        plan_query_tab()
    with tab2:
        plan_approval_tab()
    with tab3:
        execution_management_tab()
    with tab4:
        monitoring_dashboard_tab()
    with tab5:
        workflow_page()
    with tab6:
        display_full_config_status()

if __name__ == "__main__":
    main()