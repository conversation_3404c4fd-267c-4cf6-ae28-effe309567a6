import streamlit as st
from src.ui.sidebar import display_sidebar
from src.ui.tabs import plan_query_tab, plan_approval_tab, execution_management_tab, monitoring_dashboard_tab, workflow_page

def load_custom_css():
    """加载自定义CSS文件"""
    try:
        with open("src/ui/style.css", "r") as f:
            st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.warning("自定义样式文件 'src/ui/style.css' 未找到.")

def initialize_session_state():
    """Initialize session state variables."""
    if 'current_view' not in st.session_state:
        st.session_state.current_view = 'main'

def main():
    """Main function to run the Streamlit app."""
    st.set_page_config(
        page_title="智能发布助手 V2",
        page_icon="✨",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    load_custom_css()
    initialize_session_state()

    display_sidebar()

    st.title("智能发布助手")
    st.caption("一个现代、简约的发布管理工具")

    tab_titles = [
        "📝 计划",
        "✅ 审批",
        "🚀 执行",
        "📊 监控",
        "🌊 工作流"
    ]
    tab1, tab2, tab3, tab4, tab5 = st.tabs(tab_titles)

    with tab1:
        plan_query_tab()
    with tab2:
        plan_approval_tab()
    with tab3:
        execution_management_tab()
    with tab4:
        monitoring_dashboard_tab()
    with tab5:
        workflow_page()

if __name__ == "__main__":
    main()