import streamlit as st
from src.ui.sidebar import display_sidebar
from src.ui.tabs import plan_query_tab, plan_approval_tab, execution_management_tab, monitoring_dashboard_tab, workflow_page
from src.ui.config_status import display_full_config_status
from src.ui.single_page_workflow import single_page_workflow
from src.constants import APP_TITLE, APP_ICON, APP_CAPTION, TAB_TITLES
from src.release_agent import ReleaseAgent

def load_custom_css():
    """加载自定义CSS文件"""
    try:
        with open("src/ui/style.css", "r") as f:
            st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.warning("自定义样式文件 'src/ui/style.css' 未找到.")

def initialize_agent():
    """初始化Agent"""
    if 'agent' not in st.session_state or st.session_state.agent is None:
        try:
            with st.spinner("正在初始化Agent..."):
                agent = ReleaseAgent()
                st.session_state.agent = agent
            return True
        except Exception as e:
            st.error(f"❌ Agent初始化失败: {str(e)}")
            st.session_state.agent = None
            return False
    return True

def initialize_session_state():
    """Initialize session state variables."""
    if 'current_view' not in st.session_state:
        st.session_state.current_view = 'main'

def main():
    """Main function to run the Streamlit app."""
    st.set_page_config(
        page_title=APP_TITLE,
        page_icon=APP_ICON,
        layout="wide",
        initial_sidebar_state="expanded"
    )

    load_custom_css()
    initialize_session_state()

    # 初始化Agent
    agent_initialized = initialize_agent()
    if not agent_initialized:
        st.warning("⚠️ Agent未能正确初始化，部分功能可能不可用")

    display_sidebar()

    st.title("智能发布助手")
    st.caption(APP_CAPTION)

    # 添加单页面工作流和配置状态标签
    extended_tab_titles = ["🌊 工作流"] + TAB_TITLES + ["⚙️ 配置"]
    tab0, tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs(extended_tab_titles)

    with tab0:
        single_page_workflow()
    with tab1:
        plan_query_tab()
    with tab2:
        plan_approval_tab()
    with tab3:
        execution_management_tab()
    with tab4:
        monitoring_dashboard_tab()
    with tab5:
        workflow_page()
    with tab6:
        display_full_config_status()

if __name__ == "__main__":
    main()