#!/usr/bin/env python3
"""
配置测试脚本 - 验证.env配置是否被正确读取
"""

import os
import sys
from dotenv import load_dotenv

def test_env_loading():
    """测试环境变量加载"""
    print("🧪 测试环境变量加载...")
    
    # 加载.env文件
    load_dotenv()
    
    # 检查数据库配置
    db_vars = ['MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']
    print("\n📊 数据库配置:")
    for var in db_vars:
        value = os.getenv(var)
        if value:
            display_value = value if var != 'MYSQL_PASSWORD' else "***"
            print(f"  ✅ {var}: {display_value}")
        else:
            print(f"  ❌ {var}: 未设置")
    
    # 检查Jenkins配置
    jenkins_vars = ['JENKINS_URL', 'JENKINS_USERNAME', 'JENKINS_TOKEN']
    print("\n🔧 Jenkins配置:")
    for var in jenkins_vars:
        value = os.getenv(var)
        if value:
            display_value = value if var != 'JENKINS_TOKEN' else "***"
            print(f"  ✅ {var}: {display_value}")
        else:
            print(f"  ❌ {var}: 未设置")
    
    # 检查LLM配置
    llm_vars = ['GOOGLE_API_KEY', 'OPENAI_API_KEY', 'ANTHROPIC_API_KEY', 'DEFAULT_LLM_PROVIDER', 'DEFAULT_LLM_MODEL']
    print("\n🤖 LLM配置:")
    for var in llm_vars:
        value = os.getenv(var)
        if value:
            if 'API_KEY' in var:
                display_value = f"{value[:8]}..." if len(value) > 8 else "***"
            else:
                display_value = value
            print(f"  ✅ {var}: {display_value}")
        else:
            print(f"  ❌ {var}: 未设置")

def test_config_checker():
    """测试配置检查器"""
    print("\n🧪 测试配置检查器...")
    
    try:
        from src.config_checker import config_checker
        
        # 刷新状态
        config_checker.refresh_status()
        
        # 显示数据库配置状态
        print("\n📊 数据库配置状态:")
        db_config = config_checker.config_status["database"]
        print(f"  状态: {db_config['status']}")
        print(f"  消息: {db_config['message']}")
        
        # 显示Jenkins配置状态
        print("\n🔧 Jenkins配置状态:")
        jenkins_config = config_checker.config_status["jenkins"]
        print(f"  状态: {jenkins_config['status']}")
        print(f"  消息: {jenkins_config['message']}")
        
        # 显示LLM配置状态
        print("\n🤖 LLM配置状态:")
        llm_config = config_checker.config_status["llm"]
        print(f"  状态: {llm_config['status']}")
        print(f"  消息: {llm_config['message']}")
        
        # 显示总体状态
        overall_status = config_checker.get_overall_status()
        summary = config_checker.get_status_summary()
        print(f"\n📈 总体状态: {overall_status}")
        print(f"  正常: {summary['configured']}")
        print(f"  警告: {summary['warning']}")
        print(f"  错误: {summary['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查器测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🧪 测试数据库连接...")
    
    try:
        import mysql.connector
        
        connection = mysql.connector.connect(
            host=os.getenv('MYSQL_HOST'),
            port=int(os.getenv('MYSQL_PORT', 3306)),
            user=os.getenv('MYSQL_USER'),
            password=os.getenv('MYSQL_PASSWORD'),
            database=os.getenv('MYSQL_DATABASE'),
            charset='utf8mb4',
            connect_timeout=5
        )
        
        if connection.is_connected():
            print("  ✅ 数据库连接成功")
            connection.close()
            return True
        else:
            print("  ❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return False

def test_jenkins_connection():
    """测试Jenkins连接"""
    print("\n🧪 测试Jenkins连接...")
    
    try:
        import jenkins
        
        server = jenkins.Jenkins(
            url=os.getenv('JENKINS_URL'),
            username=os.getenv('JENKINS_USERNAME'),
            password=os.getenv('JENKINS_TOKEN')
        )
        
        user = server.get_whoami()
        print(f"  ✅ Jenkins连接成功 (用户: {user.get('fullName', 'Unknown')})")
        return True
        
    except Exception as e:
        print(f"  ❌ Jenkins连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始配置测试")
    print("=" * 50)
    
    tests = [
        ("环境变量加载", test_env_loading),
        ("配置检查器", test_config_checker),
        ("数据库连接", test_database_connection),
        ("Jenkins连接", test_jenkins_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}测试:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 2:  # 至少环境变量和配置检查器要通过
        print("🎉 基本配置测试通过！")
        return 0
    else:
        print("⚠️ 配置存在问题，请检查.env文件。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
